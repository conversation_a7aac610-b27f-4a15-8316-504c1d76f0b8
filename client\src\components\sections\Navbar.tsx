import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import logoImage from "@assets/logo_tokenpedia_nobg_1755041851408.png";
import { Menu, X, Home, Brain, BookOpen, User } from "lucide-react";
import { useState } from "react";
import { Link, useLocation } from "wouter";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [location] = useLocation();
  
  const handleAuthClick = () =>
    toast({
      title: "Fitur akan tersedia",
      description: "Login/Daftar aktif setelah koneksi database diaktifkan.",
    });

  const navItems = [
    { path: "/", label: "Beranda", icon: Home, hash: "#home" },
    { path: "/#features", label: "Fitur", icon: Brain, hash: "#features" },
    { path: "/assessments", label: "Assessment", icon: BookOpen },
    { path: "/#education", label: "<PERSON><PERSON><PERSON>", icon: User, hash: "#education" },
  ];

  const handleNavClick = (item: typeof navItems[0]) => {
    setIsOpen(false);
    if (item.hash) {
      if (location !== "/") {
        window.location.href = item.path;
      } else {
        document.querySelector(item.hash)?.scrollIntoView({ behavior: "smooth" });
      }
    }
  };

  return (
    <header className="w-full sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <nav className="container mx-auto flex items-center justify-between h-16 px-4">
        {/* Logo - Eye catching placement */}
        <Link href="/" className="flex items-center gap-3 hover:opacity-90 transition-opacity">
          <div className="relative">
            <img src={logoImage} alt="TokenPedia SantriMental Logo" className="h-12 w-12 rounded-full border-2 border-primary/20" />
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
          <span className="font-bold text-xl tracking-tight bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            SantriMental
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-6">
          {navItems.map((item) => (
            <Link key={item.path} href={item.path} className="group">
              <div 
                className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-primary/10 hover:text-primary cursor-pointer"
                onClick={() => handleNavClick(item)}
              >
                <item.icon size={16} />
                {item.label}
              </div>
            </Link>
          ))}
        </div>

        {/* Desktop Auth Buttons */}
        <div className="hidden md:flex items-center gap-2">
          <Button variant="outline" onClick={handleAuthClick} size="sm" className="gap-2">
            <User size={16} />
            Login
          </Button>
          <Button variant="default" onClick={handleAuthClick} size="sm">
            Daftar
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          onClick={() => setIsOpen(!isOpen)}
          data-testid="mobile-menu-toggle"
        >
          {isOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </nav>

      {/* Mobile Menu */}
      {isOpen && (
        <div className="md:hidden border-t bg-background/95 backdrop-blur">
          <div className="container mx-auto px-4 py-4 space-y-2">
            {navItems.map((item) => (
              <Link key={item.path} href={item.path}>
                <div 
                  className="flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors hover:bg-primary/10 hover:text-primary cursor-pointer"
                  onClick={() => handleNavClick(item)}
                  data-testid={`mobile-nav-${item.label.toLowerCase()}`}
                >
                  <item.icon size={18} />
                  {item.label}
                </div>
              </Link>
            ))}
            <div className="pt-4 border-t flex flex-col gap-2">
              <Button variant="outline" onClick={handleAuthClick} size="sm" className="gap-2" data-testid="mobile-login">
                <User size={16} />
                Login
              </Button>
              <Button variant="default" onClick={handleAuthClick} size="sm" data-testid="mobile-register">
                Daftar
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
