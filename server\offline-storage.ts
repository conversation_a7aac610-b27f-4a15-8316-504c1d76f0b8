import { db as mysqlDb } from './db';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';
import {
  users,
  profiles,
  assessmentSessions,
  assessmentResults,
  assessmentConfigs,
  type User,
  type Profile,
  type AssessmentSession,
  type AssessmentResult,
  type AssessmentConfig,
  type InsertUser,
  type InsertProfile,
  type InsertAssessmentSession,
  type InsertAssessmentResult
} from "@shared/schema";

export interface SyncQueueItem {
  id: string;
  tableName: string;
  recordId: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  data: any;
  createdAt: Date;
  retryCount: number;
  lastError?: string;
}

// Simple offline storage using in-memory storage with sync capability
export class OfflineStorage {
  private isOnline: boolean = false;
  private syncInProgress: boolean = false;
  private users: Map<string, User> = new Map();
  private profiles: Map<string, Profile> = new Map();
  private assessmentSessions: Map<string, AssessmentSession> = new Map();
  private assessmentResults: Map<string, AssessmentResult> = new Map();
  private assessmentConfigs: Map<string, AssessmentConfig> = new Map();
  private syncQueue: Map<string, SyncQueueItem> = new Map();

  constructor() {
    this.checkOnlineStatus();
    this.startPeriodicSync();
    this.initSampleConfigs();
  }

  private initSampleConfigs() {
    const configs: AssessmentConfig[] = [
      {
        id: crypto.randomUUID(),
        assessmentCode: 'DASS42',
        assessmentName: 'Depression Anxiety Stress Scales-42',
        description: 'Mengukur tingkat depresi, kecemasan, dan stres',
        version: '1.0',
        totalItems: 42,
        estimatedTimeMinutes: 15,
        isActive: true,
        requiresSupervision: false,
        ageMin: 12,
        ageMax: 30,
        culturalContext: 'pesantren',
        scoringMethod: 'likert',
        clinicalCutoffs: '{"depression":{"normal":9,"mild":13,"moderate":20,"severe":27}}',
        psychometricProperties: '{"reliability":0.91,"validity":"high"}',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    configs.forEach(config => this.assessmentConfigs.set(config.id, config));
  }

  private checkOnlineStatus() {
    // Check if we can connect to MySQL
    try {
      this.isOnline = !!process.env.DATABASE_URL;
    } catch {
      this.isOnline = false;
    }
  }

  private startPeriodicSync() {
    // Sync every 30 seconds when online
    setInterval(async () => {
      if (this.isOnline && !this.syncInProgress) {
        await this.syncToServer();
      }
    }, 30000);
  }

  // Add item to sync queue
  private async addToSyncQueue(tableName: string, recordId: string, operation: 'INSERT' | 'UPDATE' | 'DELETE', data: any) {
    const queueItem: SyncQueueItem = {
      id: crypto.randomUUID(),
      tableName,
      recordId,
      operation,
      data,
      createdAt: new Date(),
      retryCount: 0
    };

    this.syncQueue.set(queueItem.id, queueItem);
  }

  // User operations
  async createUser(user: InsertUser): Promise<User> {
    const userId = crypto.randomUUID();
    const userWithId = { ...user, id: userId, createdAt: new Date(), updatedAt: new Date() };

    // Save to memory
    this.users.set(userId, userWithId);

    // Add to sync queue
    await this.addToSyncQueue('users', userId, 'INSERT', userWithId);

    return userWithId;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  // Profile operations
  async createProfile(profile: InsertProfile): Promise<Profile> {
    const profileId = crypto.randomUUID();
    const profileWithId = {
      ...profile,
      id: profileId,
      createdAt: new Date(),
      updatedAt: new Date(),
      nomorInduk: profile.nomorInduk || null,
      jenisKelamin: profile.jenisKelamin || null,
      tanggalLahir: profile.tanggalLahir || null,
      kelas: profile.kelas || null,
      pondokPesantren: profile.pondokPesantren || null
    };

    // Save to memory
    this.profiles.set(profileId, profileWithId);

    // Add to sync queue
    await this.addToSyncQueue('profiles', profileId, 'INSERT', profileWithId);

    return profileWithId;
  }

  async getProfile(userId: string): Promise<Profile | undefined> {
    return Array.from(this.profiles.values()).find(profile => profile.userId === userId);
  }

  async updateProfile(userId: string, profile: Partial<InsertProfile>): Promise<Profile | undefined> {
    const existingProfile = await this.getProfile(userId);
    if (!existingProfile) return undefined;

    const updatedProfile = {
      ...existingProfile,
      ...profile,
      updatedAt: new Date()
    };

    this.profiles.set(existingProfile.id, updatedProfile);
    await this.addToSyncQueue('profiles', existingProfile.id, 'UPDATE', updatedProfile);

    return updatedProfile;
  }

  // Assessment operations
  async createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession> {
    const sessionId = crypto.randomUUID();
    const sessionWithId = {
      ...session,
      id: sessionId,
      sessionStatus: session.sessionStatus || 'started',
      startedAt: session.startedAt || new Date(),
      completedAt: session.completedAt || null,
      durationSeconds: session.durationSeconds || null,
      ipAddress: session.ipAddress || null,
      userAgent: session.userAgent || null,
      deviceType: session.deviceType || null,
      isSupervised: session.isSupervised || false,
      supervisorId: session.supervisorId || null,
      notes: session.notes || null
    };

    // Save to memory
    this.assessmentSessions.set(sessionId, sessionWithId);

    // Add to sync queue
    await this.addToSyncQueue('assessment_sessions', sessionId, 'INSERT', sessionWithId);

    return sessionWithId;
  }

  async createAssessmentResult(result: InsertAssessmentResult): Promise<AssessmentResult> {
    const resultId = crypto.randomUUID();
    const resultWithId = {
      ...result,
      id: resultId,
      createdAt: new Date(),
      updatedAt: new Date(),
      totalRawScore: result.totalRawScore || null,
      domainScores: result.domainScores || null,
      totalTScore: result.totalTScore || null,
      totalPercentile: result.totalPercentile || null,
      domainTScores: result.domainTScores || null,
      domainPercentiles: result.domainPercentiles || null,
      overallSeverity: result.overallSeverity || null,
      domainSeverities: result.domainSeverities || null,
      riskLevel: result.riskLevel || 'low',
      interpretationSummary: result.interpretationSummary || null,
      clinicalRecommendations: result.clinicalRecommendations || null,
      referralRecommended: result.referralRecommended || false,
      followUpRecommended: result.followUpRecommended || false,
      followUpTimeframe: result.followUpTimeframe || null,
      reliabilityAlpha: result.reliabilityAlpha || null,
      responseConsistency: result.responseConsistency || null,
      completionPercentage: result.completionPercentage || '100.00',
      validityFlags: result.validityFlags || null,
      religiousCopingIndicators: result.religiousCopingIndicators || null,
      culturalConsiderations: result.culturalConsiderations || null
    };

    // Save to memory
    this.assessmentResults.set(resultId, resultWithId);

    // Add to sync queue
    await this.addToSyncQueue('assessment_results', resultId, 'INSERT', resultWithId);

    return resultWithId;
  }

  async getAssessmentResultsByUser(userId: string): Promise<AssessmentResult[]> {
    return Array.from(this.assessmentResults.values()).filter(result => result.userId === userId);
  }

  async getAssessmentConfigs(): Promise<AssessmentConfig[]> {
    return Array.from(this.assessmentConfigs.values()).filter(config => config.isActive);
  }

  // Sync operations
  async syncToServer(): Promise<void> {
    if (!this.isOnline || this.syncInProgress) return;

    this.syncInProgress = true;
    console.log('🔄 Starting sync to server...');

    try {
      const pendingItems = Array.from(this.syncQueue.values()).slice(0, 50);

      for (const item of pendingItems) {
        try {
          await this.syncItem(item);
          this.syncQueue.delete(item.id);
        } catch (error: any) {
          item.retryCount++;
          item.lastError = error.message;
          console.error(`❌ Sync failed for ${item.tableName}:${item.recordId}:`, error.message);
        }
      }

      console.log(`✅ Sync completed. Processed ${pendingItems.length} items.`);
    } catch (error) {
      console.error('❌ Sync process failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncItem(item: SyncQueueItem): Promise<void> {
    switch (item.tableName) {
      case 'users':
        if (item.operation === 'INSERT') {
          await mysqlDb.insert(users).values(item.data);
        }
        break;
      case 'profiles':
        if (item.operation === 'INSERT') {
          await mysqlDb.insert(profiles).values(item.data);
        }
        break;
      case 'assessment_sessions':
        if (item.operation === 'INSERT') {
          await mysqlDb.insert(assessmentSessions).values(item.data);
        }
        break;
      case 'assessment_results':
        if (item.operation === 'INSERT') {
          await mysqlDb.insert(assessmentResults).values(item.data);
        }
        break;
    }
  }

  // Get sync status
  async getSyncStatus(): Promise<{ pending: number; lastSync: Date | null; isOnline: boolean }> {
    return {
      pending: this.syncQueue.size,
      lastSync: new Date(),
      isOnline: this.isOnline
    };
  }
}
