// Simple API test script
const testSignup = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const data = await response.json();
    console.log('Signup response:', data);
    
    if (response.ok) {
      console.log('✅ Signup successful!');
      return data.access_token;
    } else {
      console.log('❌ Signup failed:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
    return null;
  }
};

const testProfile = async (token) => {
  if (!token) return;
  
  try {
    const response = await fetch('http://localhost:5000/api/profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        namaLengkap: 'Ahmad Test',
        nomorInduk: '12345',
        jeni<PERSON><PERSON><PERSON><PERSON>: 'laki-laki',
        kelas: '3 Aliyah',
        pondokPesantren: 'Pondok Test'
      })
    });
    
    const data = await response.json();
    console.log('Profile response:', data);
    
    if (response.ok) {
      console.log('✅ Profile creation successful!');
    } else {
      console.log('❌ Profile creation failed:', data.message);
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
};

// Run tests
(async () => {
  console.log('🧪 Testing SantriMental API...\n');
  
  const token = await testSignup();
  await testProfile(token);
  
  console.log('\n✨ API tests completed!');
})();
