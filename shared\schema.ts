import { mysqlTable, text, serial, int, boolean, datetime, json, varchar } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = mysqlTable("users", {
  id: varchar("id", { length: 36 }).primaryKey(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  password: text("password").notNull(),
  createdAt: datetime("created_at").defaultNow().notNull(),
  updatedAt: datetime("updated_at").defaultNow().notNull(),
});

export const profiles = mysqlTable("profiles", {
  id: varchar("id", { length: 36 }).primaryKey(),
  userId: varchar("user_id", { length: 36 }).notNull().references(() => users.id),
  namaLengkap: varchar("nama_lengkap", { length: 255 }).notNull(),
  nomorInduk: varchar("nomor_induk", { length: 50 }),
  jenisKelamin: varchar("jenis_kelamin", { length: 10 }),
  tanggalLahir: varchar("tanggal_lahir", { length: 20 }),
  kelas: varchar("kelas", { length: 20 }),
  pondokPesantren: varchar("pondok_pesantren", { length: 255 }),
  createdAt: datetime("created_at").defaultNow().notNull(),
  updatedAt: datetime("updated_at").defaultNow().notNull(),
});

export const assessments = mysqlTable("assessments", {
  id: varchar("id", { length: 36 }).primaryKey(),
  userId: varchar("user_id", { length: 36 }).notNull().references(() => users.id),
  jenisAssessment: varchar("jenis_assessment", { length: 50 }).notNull(),
  hasilJawaban: json("hasil_jawaban").notNull(),
  skorTotal: int("skor_total"),
  interpretasi: text("interpretasi"),
  tanggalAssessment: datetime("tanggal_assessment").defaultNow().notNull(),
  createdAt: datetime("created_at").defaultNow().notNull(),
  updatedAt: datetime("updated_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  email: true,
  password: true,
});

export const insertProfileSchema = createInsertSchema(profiles).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAssessmentSchema = createInsertSchema(assessments).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type Profile = typeof profiles.$inferSelect;
export type InsertProfile = z.infer<typeof insertProfileSchema>;
export type Assessment = typeof assessments.$inferSelect;
export type InsertAssessment = z.infer<typeof insertAssessmentSchema>;
