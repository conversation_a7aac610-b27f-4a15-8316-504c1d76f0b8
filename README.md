# SantriMental - Mental Health Assessment Platform for Islamic Boarding Schools

A comprehensive mental health assessment and support platform specifically designed for students (santri) in Islamic boarding schools (pesantren).

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher)
- **MySQL** (v8.0 or higher)
- **npm** or **yarn**

### 1. Database Setup

#### Option A: Manual MySQL Setup (Recommended)

1. **Create Database**
   ```bash
   mysql -u root -p
   ```
   ```sql
   CREATE DATABASE santrimental6;
   USE santrimental6;
   ```

2. **Run Schema**
   ```bash
   mysql -u root -p santrimental6 < database/mysql-schema.sql
   ```

#### Option B: Automated Setup

1. **Install MySQL2**
   ```bash
   npm install mysql2
   ```

2. **Configure Environment**
   Create `.env` file:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=your_password
   DB_NAME=santrimental6
   DB_PORT=3306
   ```

3. **Run Setup Script**
   ```bash
   node database/setup-mysql.js
   ```

### 2. Environment Configuration

Create `.env` file in the root directory:

```env
# Database
DATABASE_URL="mysql://root:your_password@localhost:3306/santrimental6"

# Next.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Email (for notifications)
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_FROM="<EMAIL>"

# App Settings
NODE_ENV="development"
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## 📁 Project Structure

```
santri-mental/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── ...
├── components/            # React components
├── lib/                   # Utility functions
├── database/              # Database files
│   ├── mysql-schema.sql   # MySQL schema
│   └── setup-mysql.js     # Setup script
├── shared/                # Shared types and schemas
└── public/               # Static assets
```

## 🔧 Database Schema

### Core Tables
- **users**: User authentication and roles
- **profiles**: Detailed user profiles for santri
- **assessment_configs**: Assessment configurations
- **assessment_sessions**: Individual assessment attempts
- **assessment_results**: Assessment results and interpretations
- **crisis_alerts**: Crisis detection and alerts

### Assessment Tools
The platform includes 6 validated assessment tools:
- **DASS42**: Depression, Anxiety, Stress Scale
- **GSE**: General Self-Efficacy Scale
- **MHKQ**: Mental Health Knowledge Questionnaire
- **MSPSS**: Multidimensional Scale of Perceived Social Support
- **PDD**: Perceived Devaluation-Discrimination Scale
- **SRQ20**: Self-Reporting Questionnaire (WHO standard)

## 🎯 Features

### For Students (Santri)
- **Self-Assessment**: Complete mental health assessments
- **Progress Tracking**: Monitor mental health over time
- **Educational Content**: Access Islamic-based mental health resources
- **Crisis Support**: Get immediate help when needed

### For Counselors
- **Student Dashboard**: View assigned students and their status
- **Assessment Results**: Detailed analysis and interpretations
- **Crisis Alerts**: Real-time notifications for high-risk students
- **Progress Monitoring**: Track student improvement over time

### For Administrators
- **User Management**: Manage students, counselors, and admins
- **System Analytics**: Overall platform usage and insights
- **Content Management**: Manage educational modules
- **Crisis Management**: Monitor and respond to crisis situations

## 🔐 Authentication

The platform uses NextAuth.js with credentials-based authentication:
- **Students**: Register with email and student ID
- **Counselors**: Admin-assigned accounts
- **Admins**: System administrators

## 📊 Assessment Process

1. **Registration**: Students create accounts with pesantren details
2. **Assessment**: Complete validated mental health assessments
3. **Analysis**: Automatic scoring and risk assessment
4. **Interpretation**: Islamic-contextualized results
5. **Support**: Connect with counselors if needed
6. **Follow-up**: Track progress over time

## 🚨 Crisis Detection

The system automatically detects crisis situations based on:
- Assessment scores above clinical thresholds
- Risk indicators in responses
- Pattern analysis over time
- Self-harm indicators

## 📱 Responsive Design

The platform is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- Low-bandwidth environments

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev

# Build
npm run build

# Start production
npm start

# Database migrations
npm run db:push
npm run db:studio
```

### Adding New Assessments

1. Add configuration to `assessment_configs` table
2. Create assessment questions in your frontend
3. Add scoring logic in the API
4. Update result interpretation

### Customizing for Your Pesantren

1. Update pesantren-specific content in profiles
2. Customize assessment cultural context
3. Add local counselor information
4. Configure crisis response protocols

## 🔍 Troubleshooting

### Database Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection
mysql -u root -p -e "SELECT 1"

# Check database exists
mysql -u root -p -e "SHOW DATABASES LIKE 'santrimental6'"
```

### Common Issues

1. **"Access denied for user"**
   - Check MySQL credentials in `.env`
   - Ensure user has proper permissions

2. **"Database not found"**
   - Run database setup script
   - Verify database name matches `.env`

3. **"Table doesn't exist"**
   - Run schema import again
   - Check for SQL errors during setup

## 📞 Support

For technical support:
- Check the troubleshooting section
- Review MySQL logs: `/var/log/mysql/error.log`
- Check application logs in development mode

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Islamic Psychology Research Center
- Indonesian Mental Health Association
- Pesantren Mental Health Initiative
- WHO Mental Health Guidelines