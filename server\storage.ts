import { eq } from "drizzle-orm";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { 
  users, 
  profiles, 
  assessments, 
  type User, 
  type Profile, 
  type Assessment,
  type InsertUser, 
  type InsertProfile, 
  type InsertAssessment 
} from "@shared/schema";

const connectionString = process.env.DATABASE_URL!;
const sql = postgres(connectionString);
const db = drizzle(sql);

export interface IStorage {
  // User management
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Profile management
  getProfile(userId: string): Promise<Profile | undefined>;
  createProfile(profile: InsertProfile): Promise<Profile>;
  updateProfile(userId: string, profile: Partial<InsertProfile>): Promise<Profile | undefined>;
  
  // Assessment management
  createAssessment(assessment: InsertAssessment): Promise<Assessment>;
  getAssessmentsByUser(userId: string): Promise<Assessment[]>;
  getAssessment(id: string): Promise<Assessment | undefined>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const result = await db.insert(users).values(user).returning();
    return result[0];
  }

  async getProfile(userId: string): Promise<Profile | undefined> {
    const result = await db.select().from(profiles).where(eq(profiles.userId, userId)).limit(1);
    return result[0];
  }

  async createProfile(profile: InsertProfile): Promise<Profile> {
    const result = await db.insert(profiles).values(profile).returning();
    return result[0];
  }

  async updateProfile(userId: string, profile: Partial<InsertProfile>): Promise<Profile | undefined> {
    const result = await db.update(profiles)
      .set({ ...profile, updatedAt: new Date() })
      .where(eq(profiles.userId, userId))
      .returning();
    return result[0];
  }

  async createAssessment(assessment: InsertAssessment): Promise<Assessment> {
    const result = await db.insert(assessments).values(assessment).returning();
    return result[0];
  }

  async getAssessmentsByUser(userId: string): Promise<Assessment[]> {
    return await db.select().from(assessments).where(eq(assessments.userId, userId));
  }

  async getAssessment(id: string): Promise<Assessment | undefined> {
    const result = await db.select().from(assessments).where(eq(assessments.id, id)).limit(1);
    return result[0];
  }
}

// Database storage implementation is available above
export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private profiles: Map<string, Profile>;
  private assessments: Map<string, Assessment>;

  constructor() {
    this.users = new Map();
    this.profiles = new Map();
    this.assessments = new Map();
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = crypto.randomUUID();
    const user: User = { 
      ...insertUser, 
      id, 
      createdAt: new Date(), 
      updatedAt: new Date() 
    };
    this.users.set(id, user);
    return user;
  }

  async getProfile(userId: string): Promise<Profile | undefined> {
    return Array.from(this.profiles.values()).find(profile => profile.userId === userId);
  }

  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    const id = crypto.randomUUID();
    const profile: Profile = { 
      ...insertProfile,
      nomorInduk: insertProfile.nomorInduk ?? null,
      jenisKelamin: insertProfile.jenisKelamin ?? null,
      tanggalLahir: insertProfile.tanggalLahir ?? null,
      kelas: insertProfile.kelas ?? null,
      pondokPesantren: insertProfile.pondokPesantren ?? null,
      id, 
      createdAt: new Date(), 
      updatedAt: new Date() 
    };
    this.profiles.set(id, profile);
    return profile;
  }

  async updateProfile(userId: string, updateData: Partial<InsertProfile>): Promise<Profile | undefined> {
    const existing = await this.getProfile(userId);
    if (!existing) return undefined;
    
    const updated: Profile = { 
      ...existing, 
      ...updateData, 
      updatedAt: new Date() 
    };
    this.profiles.set(existing.id, updated);
    return updated;
  }

  async createAssessment(insertAssessment: InsertAssessment): Promise<Assessment> {
    const id = crypto.randomUUID();
    const assessment: Assessment = { 
      ...insertAssessment,
      skorTotal: insertAssessment.skorTotal ?? null,
      interpretasi: insertAssessment.interpretasi ?? null,
      tanggalAssessment: insertAssessment.tanggalAssessment ?? new Date(),
      id, 
      createdAt: new Date(), 
      updatedAt: new Date() 
    };
    this.assessments.set(id, assessment);
    return assessment;
  }

  async getAssessmentsByUser(userId: string): Promise<Assessment[]> {
    return Array.from(this.assessments.values()).filter(assessment => assessment.userId === userId);
  }

  async getAssessment(id: string): Promise<Assessment | undefined> {
    return this.assessments.get(id);
  }
}

// Switch to DatabaseStorage for production, MemStorage for development
export const storage = process.env.DATABASE_URL ? new DatabaseStorage() : new MemStorage();
