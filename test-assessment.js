// Assessment API test script
const testAssessment = async (token) => {
  if (!token) return;
  
  try {
    const response = await fetch('http://localhost:5000/api/assessments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `<PERSON><PERSON> ${token}`
      },
      body: JSON.stringify({
        jenisAssessment: 'DASS42',
        hasilJawaban: {
          q1: 3,
          q2: 2,
          q3: 4,
          q4: 1,
          q5: 3
        },
        skorTotal: 13,
        interpretasi: 'Tingkat stres ringan'
      })
    });
    
    const data = await response.json();
    console.log('Assessment response:', data);
    
    if (response.ok) {
      console.log('✅ Assessment creation successful!');
      return data.id;
    } else {
      console.log('❌ Assessment creation failed:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
    return null;
  }
};

const testGetAssessments = async (token) => {
  if (!token) return;
  
  try {
    const response = await fetch('http://localhost:5000/api/assessments', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    console.log('Get assessments response:', data);
    
    if (response.ok) {
      console.log(`✅ Retrieved ${data.length} assessments!`);
    } else {
      console.log('❌ Get assessments failed:', data.message);
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
};

// Login with existing user
const testLogin = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const data = await response.json();
    console.log('Login response:', data);
    
    if (response.ok) {
      console.log('✅ Login successful!');
      return data.access_token;
    } else {
      console.log('❌ Login failed:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
    return null;
  }
};

// Run assessment tests
(async () => {
  console.log('🧪 Testing SantriMental Assessment API...\n');
  
  const token = await testLogin();
  const assessmentId = await testAssessment(token);
  await testGetAssessments(token);
  
  console.log('\n✨ Assessment API tests completed!');
})();
